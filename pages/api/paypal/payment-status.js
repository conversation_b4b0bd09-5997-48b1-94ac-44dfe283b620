// Real-time Payment Status Check
export const runtime = 'edge'

export default async function handler(req) {
  console.log('Payment status check API called')

  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ success: false, message: 'Method not allowed' }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }

  try {
    // Extract query parameters from URL for Edge Runtime
    const url = new URL(req.url)
    const id = url.searchParams.get('id')
    const type = url.searchParams.get('type')

    console.log('Query params:', { id, type })
    console.log('Checking payment status for:', { id, type })

    if (!id || !type) {
      console.log('Missing required parameters')
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Payment ID and type are required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev'
    console.log('Backend URL:', backendUrl)

    let endpoint
    if (type === 'subscription') {
      endpoint = `${backendUrl}/api/paypal/subscriptions/status?subscriptionId=${id}`
    } else if (type === 'purchase') {
      endpoint = `${backendUrl}/api/paypal/purchases/status?orderId=${id}`
    } else {
      console.log('Invalid payment type:', type)
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Invalid payment type',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Calling backend endpoint:', endpoint)

    // Forward request to backend
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    console.log('Backend response status:', response.status)
    console.log(
      'Backend response headers:',
      Object.fromEntries(response.headers.entries()),
    )

    let data
    const statusCode = response.status
    const contentType = response.headers.get('content-type')

    // Check if response is JSON before parsing
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json()
        console.log('Backend response data (JSON):', data)
      } catch (jsonError) {
        console.error('Failed to parse backend response as JSON:', jsonError)
        const responseText = await response.text()
        console.log('Backend response (raw text):', responseText)

        return new Response(
          JSON.stringify({
            success: false,
            message: 'Backend returned invalid JSON',
            error: `Backend response: "${responseText}"`,
            statusCode: statusCode,
          }),
          {
            status: 502,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else {
      // Response is not JSON, get as text
      const responseText = await response.text()
      console.log('Backend response (non-JSON):', responseText)

      return new Response(
        JSON.stringify({
          success: false,
          message: 'Backend returned non-JSON response',
          error: `Backend response: "${responseText}"`,
          statusCode: statusCode,
          contentType: contentType,
        }),
        {
          status: 502,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Continue with existing logic using the parsed data
    console.log('Backend response ok:', response.ok)
    console.log('Backend response data (final):', data)

    if (!response.ok) {
      console.log('Backend error response:', data)
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Return status with additional metadata
    const finalResponse = {
      ...data,
      checkTime: new Date().toISOString(),
      paymentType: type,
    }

    console.log('Sending final response:', finalResponse)
    return new Response(JSON.stringify(finalResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Payment status check error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
