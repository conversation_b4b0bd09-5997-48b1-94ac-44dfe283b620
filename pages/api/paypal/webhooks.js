// PayPal Webhook Handler
export const runtime = 'edge'

export default async function handler(req) {
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ success: false, message: 'Method not allowed' }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }

  try {
    // Parse request body for Edge Runtime
    const requestBody = await req.json()

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev'

    // Forward webhook to backend with all headers
    const response = await fetch(`${backendUrl}/api/paypal/webhooks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward PayPal webhook headers for verification
        'paypal-transmission-sig':
          req.headers.get('paypal-transmission-sig') || '',
        'paypal-cert-url': req.headers.get('paypal-cert-url') || '',
        'paypal-transmission-id':
          req.headers.get('paypal-transmission-id') || '',
        'paypal-transmission-time':
          req.headers.get('paypal-transmission-time') || '',
      },
      body: JSON.stringify(requestBody),
    })

    console.log('Backend response status:', response.status)
    console.log(
      'Backend response headers:',
      Object.fromEntries(response.headers.entries()),
    )

    let data
    const statusCode = response.status
    const contentType = response.headers.get('content-type')

    // Check if response is JSON before parsing
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json()
        console.log('Backend response data (JSON):', data)
      } catch (jsonError) {
        console.error('Failed to parse backend response as JSON:', jsonError)
        const responseText = await response.text()
        console.log('Backend response (raw text):', responseText)

        return new Response(
          JSON.stringify({
            success: false,
            message: 'Backend returned invalid JSON',
            error: `Backend response: "${responseText}"`,
            statusCode: statusCode,
          }),
          {
            status: 502,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else {
      // Response is not JSON, get as text
      const responseText = await response.text()
      console.log('Backend response (non-JSON):', responseText)

      return new Response(
        JSON.stringify({
          success: false,
          message: 'Backend returned non-JSON response',
          error: `Backend response: "${responseText}"`,
          statusCode: statusCode,
          contentType: contentType,
        }),
        {
          status: 502,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!response.ok) {
      console.error('Backend webhook error:', data)
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Log successful webhook processing
    console.log('PayPal webhook processed successfully:', {
      eventType: requestBody.event_type,
      resourceId: requestBody.resource?.id,
      timestamp: new Date().toISOString(),
    })

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('PayPal webhook error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
