// Get PayPal Purchase Status API
export const runtime = 'edge'

export default async function handler(req) {
  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ success: false, message: 'Method not allowed' }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }

  try {
    // Extract query parameters from URL for Edge Runtime
    const url = new URL(req.url)
    const orderId = url.searchParams.get('orderId')

    if (!orderId) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Order ID is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev'

    // Forward request to backend
    const response = await fetch(
      `${backendUrl}/api/paypal/purchases/status?orderId=${orderId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )

    const data = await response.json()
    const statusCode = response.status

    if (!response.ok) {
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('PayPal purchase status error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
