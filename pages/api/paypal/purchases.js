// PayPal One-time Purchase Creation API
export const runtime = 'edge'

export default async function handler(req) {
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ success: false, message: 'Method not allowed' }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }

  try {
    // Parse request body for Edge Runtime
    const requestBody = await req.json()
    const { tier, email, amount, addons } = requestBody

    // Validate required fields
    if (!email || !amount) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Email and amount are required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get token from cookies (Edge Runtime style like other endpoints)
    const cookieHeader = req.headers.get('cookie')
    let token = null

    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    // Validate token
    if (!token) {
      console.log('Validation failed - missing token')
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Authentication required',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Token found in cookies:', token ? '***' : 'missing')

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev'
    console.log('Backend URL:', backendUrl)

    // Forward request to backend
    const response = await fetch(`${backendUrl}/api/paypal/purchases`, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tier: tier?.toLowerCase(),
        email,
        amount: parseFloat(amount),
        addons: addons || {},
      }),
    })

    console.log('Backend response status:', response.status)
    console.log('Backend response ok:', response.ok)
    console.log(
      'Backend response headers:',
      Object.fromEntries(response.headers.entries()),
    )

    let data
    const statusCode = response.status
    const contentType = response.headers.get('content-type')

    // Check if response is JSON before parsing
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json()
        console.log('Backend response data (JSON):', data)
      } catch (jsonError) {
        console.error('Failed to parse backend response as JSON:', jsonError)
        const responseText = await response.text()
        console.log('Backend response (raw text):', responseText)

        return new Response(
          JSON.stringify({
            success: false,
            message: 'Backend returned invalid JSON',
            error: `Backend response: "${responseText}"`,
            statusCode: statusCode,
          }),
          {
            status: 502,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else {
      // Response is not JSON, get as text
      const responseText = await response.text()
      console.log('Backend response (non-JSON):', responseText)

      return new Response(
        JSON.stringify({
          success: false,
          message: 'Backend returned non-JSON response',
          error: `Backend response: "${responseText}"`,
          statusCode: statusCode,
          contentType: contentType,
        }),
        {
          status: 502,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!response.ok) {
      console.log('Backend error response:', data)
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    console.log('Sending success response to frontend')
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('PayPal purchase creation error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
