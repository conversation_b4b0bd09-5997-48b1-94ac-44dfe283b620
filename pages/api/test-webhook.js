// Test endpoint untuk simulate webhook dari backend
export const runtime = 'edge'

export default async function handler(req) {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ message: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    })
  }

  try {
    // Parse request body for Edge Runtime
    const requestBody = await req.json()
    const { external_id } = requestBody

    // Simulate webhook payload dari Xendit
    const testWebhookData = {
      id: '687ba473a59bfcd85c744bd6',
      amount: 225000,
      status: 'PAID',
      created: '2025-07-19T13:58:11.753Z',
      is_high: false,
      paid_at: '2025-07-19T13:58:17.540Z',
      updated: '2025-07-19T13:58:19.325Z',
      user_id: '679e3e04efb310294787b54c',
      currency: 'IDR',
      payment_id: 'qrpy_1cc69078-60aa-4a15-bd1e-cf54036e55b1',
      description: 'Subscription for basic tier',
      external_id: external_id || 'sub_1752933490511_ygo9f7hm9',
      paid_amount: 225000,
      payer_email: '<EMAIL>',
      merchant_name: 'Grandis',
      payment_method: 'QR_CODE',
      payment_channel: 'QRIS',
      payment_details: {
        source: 'DANA',
        receipt_id: '',
      },
    }

    console.log('🧪 Sending test webhook:', testWebhookData)

    // Get origin from headers for Edge Runtime
    const origin =
      req.headers.get('origin') ||
      req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
      'http://localhost:3001'

    // Forward ke webhook listener
    const response = await fetch(`${origin}/api/xendit/webhook-listener`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testWebhookData),
    })

    const result = await response.json()

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Test webhook sent',
        webhookData: testWebhookData,
        listenerResponse: result,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Test webhook error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Test webhook failed',
        error: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
