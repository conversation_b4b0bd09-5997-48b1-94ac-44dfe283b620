// Get PayPal Subscription Status API
export const runtime = 'edge'

export default async function handler(req) {
  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ success: false, message: 'Method not allowed' }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }

  try {
    // Extract subscriptionId from URL path for Edge Runtime
    const url = new URL(req.url)
    const subscriptionId = url.pathname.split('/').pop()

    if (!subscriptionId) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Subscription ID is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev'

    // Forward request to backend
    const response = await fetch(
      `${backendUrl}/api/paypal/subscriptions/${subscriptionId}/status`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )

    console.log('Backend response status:', response.status)
    console.log(
      'Backend response headers:',
      Object.fromEntries(response.headers.entries()),
    )

    let data
    const statusCode = response.status
    const contentType = response.headers.get('content-type')

    // Check if response is JSON before parsing
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json()
        console.log('Backend response data (JSON):', data)
      } catch (jsonError) {
        console.error('Failed to parse backend response as JSON:', jsonError)
        const responseText = await response.text()
        console.log('Backend response (raw text):', responseText)

        return new Response(
          JSON.stringify({
            success: false,
            message: 'Backend returned invalid JSON',
            error: `Backend response: "${responseText}"`,
            statusCode: statusCode,
          }),
          {
            status: 502,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else {
      // Response is not JSON, get as text
      const responseText = await response.text()
      console.log('Backend response (non-JSON):', responseText)

      return new Response(
        JSON.stringify({
          success: false,
          message: 'Backend returned non-JSON response',
          error: `Backend response: "${responseText}"`,
          statusCode: statusCode,
          contentType: contentType,
        }),
        {
          status: 502,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!response.ok) {
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('PayPal subscription status error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
